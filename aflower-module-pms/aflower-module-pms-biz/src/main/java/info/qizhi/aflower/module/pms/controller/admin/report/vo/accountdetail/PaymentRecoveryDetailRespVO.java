package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 付款(结账)收回明细 Response VO")
@Data
public class PaymentRecoveryDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "付款科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("付款科目代码")
    private String subCode;

    @Schema(description = "付款科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("付款科目")
    private String subName;

    @Schema(description = "今日(结账)收回金额")
    @ExcelProperty("今日(结账)收回")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long recoveryAmount;

    @Schema(description = "房号")
    @ExcelProperty("房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人姓名")
    @ExcelProperty("客人姓名")
    private String guestName;

    @Schema(description = "结账营业日")
    @ExcelProperty("结账营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate checkoutBizDate;

    @Schema(description = "结账时间")
    @ExcelProperty("结账时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkoutTime;

    @Schema(description = "结账操作员")
    @ExcelProperty("结账操作员")
    private String checkoutOperator;

    @Schema(description = "结账操作员昵称")
    @ExcelProperty("结账操作员昵称")
    private String checkoutOperatorName;

    @Schema(description = "班次")
    @ExcelProperty("班次")
    private String shiftName;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "账务号")
    private String accNo;

    @Schema(description = "结账号")
    private String payNo;
}
