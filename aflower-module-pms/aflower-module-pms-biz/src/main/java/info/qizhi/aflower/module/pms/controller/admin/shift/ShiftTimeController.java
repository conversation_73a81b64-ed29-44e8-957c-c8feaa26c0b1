package info.qizhi.aflower.module.pms.controller.admin.shift;

import info.qizhi.aflower.framework.common.enums.ShiftTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.datapermission.core.annotation.DataPermission;
import info.qizhi.aflower.module.pms.controller.admin.shift.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.shift.ShiftTimeDO;
import info.qizhi.aflower.module.pms.service.shift.ShiftTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 班次设置")
@RestController
@RequestMapping("/pms/shift-time")
@Validated
public class ShiftTimeController {

    @Resource
    private ShiftTimeService shiftTimeService;

    @PutMapping("/update")
    @Operation(summary = "更新班次设置")
    @PreAuthorize("@ss.hasPermission('pms:shift-time:update')")
    public CommonResult<Boolean> updateShiftTime(@Valid @RequestBody List<ShiftTimeSaveReqVO> updateReqVOS) {
        shiftTimeService.updateShiftTime(updateReqVOS);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得班次设置")
    @Parameter(name = "gcode", description = "集团代码", required = true)
    @Parameter(name = "shiftCode", description = "班次代码", required = true)
    @Parameter(name = "hcode", description = "门店代码", required = true)
    @PreAuthorize("@ss.hasPermission('pms:shift-time:query')")
    public CommonResult<ShiftTimeRespVO> getShiftTime(@RequestParam("gcode") String gcode, @RequestParam("shiftCode") String shiftCode, @RequestParam("hcode") String hcode) {
        ShiftTimeDO shiftTime = shiftTimeService.getShiftTime(gcode, shiftCode, hcode);
        return success(BeanUtils.toBean(shiftTime, ShiftTimeRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得班次设置列表,切换门店时默认根据系统时间选中状态")
    //@PreAuthorize("@ss.hasPermission('pms:shift-time:query')")
    public CommonResult<List<ShiftTimeRespVO>> getShiftTimeList(@Valid ShiftTimeReqVO reqVO) {
        List<ShiftTimeDO> result = shiftTimeService.getShiftTimeList(reqVO);
        return success(convertToResponseList(result));
    }

    @PostMapping("/switch-merchant")
    @Operation(summary = "(切换门店选择班次，同时改变营业日)")
    //@PreAuthorize("@ss.hasPermission('pms:shift-time:update:switch-merchant')")
    @DataPermission(enable = false)
    public CommonResult<MerchantShiftSimpleRespVO> switchVisitMerchantAndShiftTime(@Valid @RequestBody MerchantShiftReqVO reqVO) {
        return success(shiftTimeService.switchVisitMerchantAndShiftTimeAndBizDate(reqVO));
    }

    @PostMapping("/handover")
    @Operation(summary = "交班")
    //@PreAuthorize("@ss.hasPermission('pms:shift-time:update:handover')")
    @DataPermission(enable = false)
    public CommonResult<MerchantShiftSimpleRespVO> handover(@Valid @RequestBody HandoverReqVO reqVO) {
        return success(shiftTimeService.handover(reqVO));
    }

    @GetMapping("/get-change-shift")
    @Operation(summary = "获得能交换的班次")
    //@PreAuthorize("@ss.hasPermission('pms:shift-time:update:handover')")
    @DataPermission(enable = false)
    public CommonResult<ChangeShiftTimeRespVO> getShift(@Valid HandoverReqVO reqVO) {
        return success(shiftTimeService.getShift(reqVO));
    }

    @GetMapping("/get-change-shift-by-shift-set")
    @Operation(summary = "根据门店班次参数配置获得能交换的班次")
    //@PreAuthorize("@ss.hasPermission('pms:shift-time:update:handover')")
    @DataPermission(enable = false)
    public CommonResult<List<ShiftTimeRespVO>> getChangeShiftByShiftSet(@Valid HandoverReqVO reqVO) {
        List<ShiftTimeDO> shiftTimeDOS = shiftTimeService.getChangeShiftByShiftSet(reqVO);
        return success(convertToResponseList(shiftTimeDOS));
    }


    private static List<ShiftTimeRespVO> convertToResponseList(List<ShiftTimeDO> shiftTimeList) {
        return shiftTimeList.stream()
                .map(shiftTimeDO -> {
                    ShiftTimeRespVO respVO = new ShiftTimeRespVO();
                    respVO.setId(shiftTimeDO.getId());
                    respVO.setGcode(shiftTimeDO.getGcode());
                    respVO.setHcode(shiftTimeDO.getHcode());
                    respVO.setShiftCode(shiftTimeDO.getShiftCode());
                    respVO.setStartTime(shiftTimeDO.getStartTime());
                    respVO.setEndTime(shiftTimeDO.getEndTime());
                    respVO.setState(shiftTimeDO.getState());

                    // 动态获取 label 标签
                    String messageCode =shiftTimeDO.getShiftCode(); // 使用 code 生成唯一 message key
                    String channelName  = ShiftTypeEnum.getNameByCode(messageCode);

                    // 如果 messageSource 返回的内容与 messageCode 相同，表示未找到对应的国际化内容
                    respVO.setShiftName(messageCode.equals(channelName) ? shiftTimeDO.getShiftName() : channelName);

                    return respVO;
                })
                .collect(Collectors.toList());
    }
}