package info.qizhi.aflower.module.pms.service.report;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.SettleAccountListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.order.OrderReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.together.OrderTogetherReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.settledetail.*;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.RoomTypeReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.account.AccountDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeDO;
import info.qizhi.aflower.module.pms.service.account.AccountService;
import info.qizhi.aflower.module.pms.service.accset.AccSetService;
import info.qizhi.aflower.module.pms.service.cashbillorder.CashBillOrderService;
import info.qizhi.aflower.module.pms.service.order.OrderService;
import info.qizhi.aflower.module.pms.service.order.OrderTogetherService;
import info.qizhi.aflower.module.pms.service.roomtype.RoomTypeService;
import info.qizhi.aflower.module.system.api.dict.DictDataApi;
import info.qizhi.aflower.module.system.api.dict.dto.DictDataRespDTO;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import info.qizhi.aflower.module.system.api.user.AdminUserApi;
import info.qizhi.aflower.module.system.api.user.dto.UserSimpleRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.DATE_ERROR;

/**
 * 前台结账报表 Service 实现
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SettleDetailServiceImpl implements SettleDetailService {

    public static final String ROOM = "房间账";
    public static final String CASH = "现付账";

    @Resource
    private AccountService accountService;
    @Resource
    private DictDataApi dictDataApi;
    @Resource
    private OrderTogetherService orderTogetherService;
    @Resource
    private OrderService orderService;
    @Resource
    private RoomTypeService roomTypeService;
    @Resource
    private CashBillOrderService cashBillOrderService;
    @Resource
    private AccSetService accSetService;
    @Resource
    private MerchantApi merchantApi;
    @Resource
    private AdminUserApi userApi;

    /**
     * 获得前台结账报表信息
     */
    @Override
    public SettleDetailReportRespVO getSettleDetailReport(SettleDetailReportReqVO reqVO) {
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null && convertStringToLocalDate(reqVO.getStartDate(),reqVO.getTimeType())
                .isAfter(convertStringToLocalDate(reqVO.getEndDate(), reqVO.getTimeType()))) {
            throw exception(DATE_ERROR);
        }
        SettleDetailReportRespVO settleDetailReportRespVO = new SettleDetailReportRespVO();
        List<SettleDetailRespVO> settleDetailList = CollUtil.newArrayList();
        List<SettleAccountName> payNameList = CollUtil.newArrayList();
        List<SettleAccountName> consumeNameList = CollUtil.newArrayList();
        // 填充门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        settleDetailReportRespVO.setHname(merchant.getHname())
                .setOperator(reqVO.getOperator())
                .setLastSelectTime(LocalDateTime.now());
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            settleDetailReportRespVO
                    .setStartDate(convertStringToLocalDate(reqVO.getStartDate(), reqVO.getTimeType()))
                    .setEndDate(convertStringToLocalDate(reqVO.getEndDate(), reqVO.getTimeType()));
        } else {
            settleDetailReportRespVO
                    .setStartDate(reqVO.getPayBizDate())
                    .setEndDate(reqVO.getPayBizDate());
        }

        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nicknameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        //获取房费科目
        //List<DictDataRespDTO> dictData = dictDataApi.getDictDataListByParentCode(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), ConsumeAccountEnum.ROOM_FEE.getCode()).getData();
        List<DictDataRespDTO> dictData = dictDataApi.getDictDataListByParentCode(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT_ROOM_FEE.getCode()).getData();
        // 获取房间账务
        SettleAccountListReqVO accountListReqVO = new SettleAccountListReqVO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setTimeType(reqVO.getTimeType())
                .setStartDate(reqVO.getStartDate())
                .setEndDate(reqVO.getEndDate())
                .setState(AccountStatusEnum.CLOSED.getCode())
                .setPayBizDate(reqVO.getPayBizDate())
                .setPayOperator(reqVO.getPayOperator());

        List<AccountDO> accountList = accountService.getSettleAccountList(accountListReqVO);
        // 排除所有预授权类型账务
        List<AccountDO> filteredAccountDOList = accountList.stream()
                .filter(account -> !Set.of(
                        PayAccountEnum.BANK_PRE_AUTH.getCode(),
                        PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()
                ).contains(account.getSubCode()))
                .toList();
        if (CollUtil.isNotEmpty(filteredAccountDOList)) {
            configAccountInfo(reqVO, settleDetailList, dictData, filteredAccountDOList, nicknameMap);
        }

        // 过滤入住类型
        if (StrUtil.isNotBlank(reqVO.getInType())) {
            settleDetailList = CollectionUtils.filterList(settleDetailList, item -> StrUtil.equals(item.getInType(), reqVO.getInType()));
        }
        // 过滤结账类型
        if (StrUtil.isNotBlank(reqVO.getPayType())) {
            settleDetailList = CollectionUtils.filterList(settleDetailList, item -> StrUtil.equals(item.getPayType(), reqVO.getPayType()));
        }
        //configSettleDetailData(settleDetailList, settleDetailReportRespVO, payNameList, consumeNameList);
        settleDetailReportRespVO.setList(settleDetailList);
        return settleDetailReportRespVO;
    }

    private void configSettleDetailData(List<SettleDetailRespVO> settleDetailList, SettleDetailReportRespVO settleDetailReportRespVO, List<SettleAccountName> payNameList, List<SettleAccountName> consumeNameList) {
        settleDetailList.forEach(item -> {
            List<SubCodeAccount> payAccountList = new ArrayList<>();
            List<SubCodeAccount> consumeAccountList = new ArrayList<>();
            item.getPayAccountList().forEach(payAccount -> {
                payNameList.forEach(payName -> {
                    if(payName.getSubCode().equals(payAccount.getSubCode())){
                        payAccountList.add(payAccount);
                    }else {
                        SubCodeAccount subCodeAccount = new SubCodeAccount();
                        subCodeAccount.setSubCode(payName.getSubCode());
                        subCodeAccount.setSubName(payName.getSubName());
                        payAccountList.add(subCodeAccount);
                    }
                });
            });

            item.getConsumeAccountList().forEach(consumeAccount -> {
                consumeNameList.forEach(payName -> {
                    if(payName.getSubCode().equals(consumeAccount.getSubCode())){
                        consumeAccountList.add(consumeAccount);
                    }else {
                        SubCodeAccount subCodeAccount = new SubCodeAccount();
                        subCodeAccount.setSubCode(payName.getSubCode());
                        subCodeAccount.setSubName(payName.getSubName());
                        consumeAccountList.add(subCodeAccount);
                    }
                });
            });


            item.setPayAccountList(payAccountList);
        });
    }

    private void configAccountInfo(SettleDetailReportReqVO reqVO, List<SettleDetailRespVO> settleDetailList, List<DictDataRespDTO> dictData,
                                   List<AccountDO> accountList, Map<String, String> nicknameMap) {
        List<String> togetherCodes = CollectionUtils.convertList(accountList, AccountDO::getTogetherCode).stream().distinct().toList();
        List<String> orderNos = CollectionUtils.convertList(accountList, AccountDO::getNo).stream().distinct().toList();

        // 获取订单
        // 获取宾客订单
        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setTogetherCodes(togetherCodes));
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNos));
        Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
        Map<String, OrderTogetherDO> orderTogetherMap = CollectionUtils.convertMap(orderTogetherList, OrderTogetherDO::getTogetherCode);

        //获取现付账列表
        List<AccountDO> cashAccountList = CollectionUtils.filterList(accountList, account -> account.getSubType().equals(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode())
                && account.getAccType().equals(AccountTypeEnum.CASH.getCode()));
        Map<String, AccountDO> cashAccountMap = CollectionUtils.convertMap(cashAccountList, AccountDO::getTogetherCode);

        // 获取房型
        List<RoomTypeDO> roomTypeList = roomTypeService.getRoomTypeList(new RoomTypeReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()), false);
        Map<String, String> rtNameMap = CollectionUtils.convertMap(roomTypeList, RoomTypeDO::getRtCode, RoomTypeDO::getRtName);
        Map<LocalDate, Map<String, List<AccountDO>>> groupedByDateAndTogetherCode = accountList.stream()
                .collect(Collectors.groupingBy(
                        AccountDO::getPayBizDate, // 按 getPayBizDate 分组
                        Collectors.groupingBy(AccountDO::getTogetherCode) // 在每个日期组内按 getTogetherCode 分组
                ));

        List<SettleDetailRespVO> settleDetailAccountList = new ArrayList<>();
        groupedByDateAndTogetherCode.entrySet().stream()
                .forEach(dateEntry -> {
                    // 获取每个日期组
                    LocalDate payBizDate = dateEntry.getKey();
                    Map<String, List<AccountDO>> togetherCodeGroup = dateEntry.getValue();

                    //处理每个日期组下的 getTogetherCode 分类
                    togetherCodeGroup.entrySet().stream()
                            .map(togetherCodeEntry -> {
                                List<AccountDO> list = togetherCodeEntry.getValue();
                                String key = togetherCodeEntry.getKey();
                                //  消费科目
                                long roomFee = 0;
                                for (DictDataRespDTO dictDatum : dictData) {
                                    long sum = CollectionUtils.filterList(list, o -> dictDatum.getCode().equals(o.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                    roomFee += sum;
                                }
                                // 餐饮消费金额
                                long cateringFee = CollectionUtils.filterList(list, item -> ConsumeAccountEnum.CATERING.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 小商品消费金额
                                long goodsFee = CollectionUtils.filterList(list, item -> ConsumeAccountEnum.GOODS.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 会员卡消费金额
                                long memberCardFee = CollectionUtils.filterList(list, item -> ConsumeAccountEnum.MEMBER_CARD.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 会议费
                                long meetingFee = CollectionUtils.filterList(list, item -> ConsumeAccountEnum.MEETING_FEE.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 优惠券抵扣
                                long couponDeduction = CollectionUtils.filterList(list, item -> ConsumeAccountEnum.COUPON_DEDUCTION.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 其他消费
                                long consumeSum = CollectionUtils.filterList(list, item -> DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(item.getSubType())).stream().mapToLong(AccountDO::getFee).sum();
                                long consSubOth = consumeSum - (roomFee + cateringFee + goodsFee + memberCardFee + meetingFee+ couponDeduction);
                                //  付款科目
                                // 人民币现金收款
                                long rmbReceipt = CollectionUtils.filterList(list, item -> PayAccountEnum.RMB_RECEIPT.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 人名币押金
                                long rmbDeposit = CollectionUtils.filterList(list, item -> PayAccountEnum.RMB_DEPOSIT.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 现金退款
                                long cashRefund = CollectionUtils.filterList(list, item -> PayAccountEnum.CASH_REFUND.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 银行卡
                                long bankCard = CollectionUtils.filterList(list, item -> PayAccountEnum.BANK_CARD.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 储值卡
                                long storeCard = CollectionUtils.filterList(list, item -> PayAccountEnum.STORE_CARD.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // AR账
                                long creditAccount = CollectionUtils.filterList(list, item -> PayAccountEnum.CREDIT_S_ACCOUNT.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 微信
                                long wx = CollectionUtils.filterList(list, item -> PayAccountEnum.SCAN_GUN_WX.getCode().equals(item.getSubCode()) ||
                                        PayAccountEnum.WX.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 支付宝
                                long alipay = CollectionUtils.filterList(list, item -> PayAccountEnum.SCAN_GUN_ALIPAY.getCode().equals(item.getSubCode()) ||
                                        PayAccountEnum.ALIPAY.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 信用住
                                long alitrap = CollectionUtils.filterList(list, item -> PayAccountEnum.ALITRAP.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 合并结账
                                long mergeAccount = CollectionUtils.filterList(list, item -> PayAccountEnum.MERGE_ACCOUNT.getCode().equals(item.getSubCode())).stream().mapToLong(AccountDO::getFee).sum();
                                // 其他
                                long paySum = CollectionUtils.filterList(list, item -> DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(item.getSubType())).stream().mapToLong(AccountDO::getFee).sum();
                                long paySubOth = paySum - (rmbReceipt + rmbDeposit + cashRefund + bankCard + creditAccount + wx + alipay + alitrap + mergeAccount + storeCard);

                                // 创建两个列表分别存储付款科目和消费科目的费用
                                List<SubCodeAccount> payAccountList = new ArrayList<>();
                                List<SubCodeAccount> consumeAccountList = new ArrayList<>();
                                //long mergeAccount=0L;

                                // 将账单按 subType 分组，分别处理付款科目和消费科目
                                Map<String, List<AccountDO>> groupedBySubType = CollectionUtils.convertMultiMap(list, AccountDO::getSubType);

                               /* // 处理消费科目
                                List<AccountDO> consumeAccounts = groupedBySubType.get(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode()); // 消费科目
                                if (CollUtil.isNotEmpty(consumeAccounts)) {
                                    // 按 subCode 分组，并累加费用
                                    Map<String, List<AccountDO>> consumeGroupedBySubCode = CollectionUtils.convertMultiMap(consumeAccounts, AccountDO::getSubCode);
                                    for (Map.Entry<String, List<AccountDO>> account : consumeGroupedBySubCode.entrySet()) {
                                        String subCode = account.getKey();
                                        long totalFee = account.getValue().stream().mapToLong(AccountDO::getFee).sum();
                                        if (totalFee != 0) {
                                            if (consumeNameList.stream().noneMatch(item -> item.getSubCode().equals(subCode))) {
                                                SettleAccountName accountName = new SettleAccountName();
                                                accountName.setSubCode(subCode);
                                                accountName.setSubName(ConsumeAccountEnum.getLabelByCode(subCode));
                                                consumeNameList.add(accountName);
                                            }
                                            SubCodeAccount subCodeAccount = new SubCodeAccount();
                                            subCodeAccount.setSubCode(subCode).setSubName(ConsumeAccountEnum.getLabelByCode(subCode))
                                                    .setFee(totalFee);
                                            consumeAccountList.add(subCodeAccount);
                                        }
                                    }
                                }

                                // 处理付款科目
                                List<AccountDO> payAccounts = groupedBySubType.get(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode()); // 付款科目
                                if (CollUtil.isNotEmpty(payAccounts)) {
                                    // 按 subCode 分组，并累加费用
                                    Map<String, List<AccountDO>> payGroupedBySubCode = CollectionUtils.convertMultiMap(payAccounts, AccountDO::getSubCode);
                                    for (Map.Entry<String, List<AccountDO>> account : payGroupedBySubCode.entrySet()) {
                                        String subCode = account.getKey();
                                        if (PayAccountEnum.MERGE_ACCOUNT.getCode().equals(subCode)) {
                                            mergeAccount = account.getValue().stream().mapToLong(AccountDO::getFee).sum();

                                        } else {
                                            long totalFee = account.getValue().stream().mapToLong(AccountDO::getFee).sum();
                                            if (totalFee != 0) {
                                                if (payNameList.stream().noneMatch(item -> item.getSubCode().equals(subCode))) {
                                                    SettleAccountName accountName = new SettleAccountName();
                                                    accountName.setSubCode(subCode);
                                                    accountName.setSubName(PayAccountEnum.getLabelByCode(subCode));
                                                    payNameList.add(accountName);
                                                }
                                                SubCodeAccount subCodeAccount = new SubCodeAccount();
                                                subCodeAccount.setSubCode(subCode).setSubName(PayAccountEnum.getLabelByCode(subCode))
                                                        .setFee(totalFee);
                                                payAccountList.add(subCodeAccount);
                                            }
                                        }
                                    }
                                }*/
                                LocalDateTime payTime = orderTogetherMap.getOrDefault(key, new OrderTogetherDO()).getPayTime();
                                OrderTogetherDO togetherDO = orderTogetherMap.get(key);
                                AccountDO cashAccountDO = cashAccountMap.getOrDefault(key, new AccountDO());
                                OrderTogetherDO orderTogetherDO = orderTogetherMap.get(key);

                                String url = orderTogetherDO == null ? "" : String.format(
                                        OrderUrlEnum.URL.getCode(),
                                        orderTogetherDO.getOrderNo(),
                                        OrderStateEnum.IN_BOOKING.getCode().equals(orderTogetherDO.getState()) ? NoTypeEnum.BOOK.getCode() : NoTypeEnum.ORDER.getCode()
                                );

                                SettleDetailRespVO settleDetailRespVO = new SettleDetailRespVO();
                                settleDetailRespVO
                                        .setGcode(reqVO.getGcode())
                                        .setHcode(reqVO.getHcode())
                                        .setOrderNo(orderTogetherMap.getOrDefault(key, new OrderTogetherDO()).getOrderNo())
                                        .setUrl(url)
                                        .setRNo(orderTogetherMap.getOrDefault(key, new OrderTogetherDO()).getRNo())
                                        .setTogetherCode(key)
                                        .setName(togetherDO==null? CASH+"-"+ConsumeAccountEnum.getLabelByCode(cashAccountDO.getSubCode()) : togetherDO.getName())
                                        .setRtCode(orderMap.getOrDefault(settleDetailRespVO.getOrderNo(), new OrderDO()).getRtCode())
                                        .setRtName(rtNameMap.getOrDefault(settleDetailRespVO.getRtCode(), ""))
                                        .setGSrc(orderMap.getOrDefault(settleDetailRespVO.getOrderNo(), new OrderDO()).getGuestSrcType())
                                        .setGSrcName(GuestSrcTypeEnum.getLabelByCode(settleDetailRespVO.getGSrc()))
                                        .setInType(orderTogetherMap.getOrDefault(key, new OrderTogetherDO()).getCheckinType())
                                        .setInTypeName(CheckInTypeEnum.getNameByCode(settleDetailRespVO.getInType()))
                                        .setCheckInTime(orderTogetherMap.getOrDefault(key, new OrderTogetherDO()).getCheckinTime())
                                        .setCheckOutTime(orderTogetherMap.getOrDefault(key, new OrderTogetherDO()).getCheckoutTime())
                                        .setPayTime(payTime)
                                        .setPayBizDate(payBizDate)
                                        .setPayType(togetherDO==null? CASH : ROOM)
                                        //.setPayAccountList(payAccountList)
                                        //.setConsumeAccountList(consumeAccountList)
                                        .setConsSubFee(roomFee)
                                        .setConsSubCate(cateringFee)
                                        .setConsSubGoods(goodsFee)
                                        .setConsSubCard(memberCardFee)
                                        .setConsSubMeetroom(meetingFee)
                                        .setCouponDeduction(couponDeduction)
                                        .setConsSubOth(consSubOth)
                                        .setConsumeTotalFee(consumeSum)
                                        .setPaySubCash(rmbReceipt)
                                        .setPaySubDeposit(rmbDeposit)
                                        .setPaySubCashBack(cashRefund)
                                        .setPaySubBank(bankCard)
                                        .setPaySubStoreCard(storeCard)
                                        .setPaySubAr(creditAccount)
                                        .setPaySubWx(wx)
                                        .setPaySubAlpay(alipay)
                                        .setPaySubCredit(alitrap)
                                        .setPaySubOth(paySubOth)
                                        .setPayTotalFee(paySum - mergeAccount)
                                        .setMerge(mergeAccount)
                                        /*.setConsumeAccountList(consumeAccountList)
                                        .setPayAccountList(payAccountList)*/
                                    .setPayOperator(orderTogetherMap.getOrDefault(key, new OrderTogetherDO()).getPayOperator())
                                    .setPayOperatorName(nicknameMap.getOrDefault(settleDetailRespVO.getPayOperator(), settleDetailRespVO.getPayOperator()));

                                return settleDetailRespVO;
                            })
                            .forEach(settleDetailAccountList::add);
                });


        settleDetailList.addAll(settleDetailAccountList);
    }


    public LocalDate convertStringToLocalDate(String dateString, String timeType) {
        if (dateString == null || dateString.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter;
            if (timeType.equals(NumberEnum.TWO.getNumber())) {
                // 日期字符串包含时间部分
                formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
                // 解析为 LocalDateTime，然后提取 LocalDate
                return LocalDateTime.parse(dateString, formatter).toLocalDate();
            } else {
                // 只有日期部分
                formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY);
                return LocalDate.parse(dateString, formatter);
            }
        } catch (DateTimeParseException e) {
            // 处理日期格式错误的情况
            throw new IllegalArgumentException("日期格式无效。请使用'yyyy-MM-dd'或'yyyy-MM-dd HH:mm:ss'格式", e);
        }
    }
}
